package com.ruoyi.custom.admin.elderlyPeople.controller;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.domain.vo.ElderlyPeopleInfoLivingVo;
import com.ruoyi.custom.admin.elderlyPeople.domain.vo.ElderlyPeopleInfoVo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.marketing.domain.ContractInfo;
import com.ruoyi.custom.admin.marketing.service.ContractInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 老人基础信息Controller
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
@RestController
@RequestMapping("/elderlyPeopleInfo")
@Api(value = "老人基础信息Controller", tags = {"老人基础信息"})
public class ElderlyPeopleInfoController extends BaseController {
    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    @Autowired
    private ContractInfoService contractInfoService;

    /**
     * 查询老人基础信息列表
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleInfo:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询老人基础信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "name", value = "姓名", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "sex", value = "性别", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "idCardNum", value = "身份证号", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "params[beginAge]", value = "开始年龄", required = false, dataTypeClass = Integer.class),
            @ApiImplicitParam(paramType = "query", name = "params[endAge]", value = "结束年龄", required = false, dataTypeClass = Integer.class),
            @ApiImplicitParam(paramType = "query", name = "status", value = "入住状态：0入住中，1未入住，2请假中，3已退住", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "phone", value = "手机号", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo list(@ApiIgnore ElderlyPeopleInfo elderlyPeopleInfo) {
        startPage();
        List<ElderlyPeopleInfo> list = elderlyPeopleInfoService.selectElderlyPeopleInfoList(elderlyPeopleInfo);
        return getDataTable(list);
    }


    /**
     * 查询办理过入住的老人信息模块列表
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleInfo:list")
    @GetMapping("/getPeopleInfoList")
    @ApiOperation(value = "查询办理过入住的老人信息模块列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userName", value = "姓名", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "state", value = "入住状态：0入住中，1未入住", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "liveDateBegin", value = "入住日期开始只做查询", dataTypeClass = java.util.Date.class),
            @ApiImplicitParam(paramType = "query", name = "liveDateEnd", value = "入住日期结束只做查询", dataTypeClass = java.util.Date.class),
            @ApiImplicitParam(paramType = "query", name = "expiredDateBegin", value = "合同到期日期开始只做查询", dataTypeClass = java.util.Date.class),
            @ApiImplicitParam(paramType = "query", name = "expiredDateEnd", value = "合同到期日期结束只做查询", dataTypeClass = java.util.Date.class),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", dataTypeClass = String.class),
    })
    public TableDataInfo<ElderlyPeopleInfoVo> getPeopleInfoList(@ApiIgnore ElderlyPeopleInfoVo elderlyPeopleInfoVo) {
        startPage();
        List<ElderlyPeopleInfoVo> list = elderlyPeopleInfoService.getPeopleInfoList(elderlyPeopleInfoVo);
        return getDataTable(list);
    }

    /**
     * 查询正在入住的老人信息模块列表
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleInfo:list")
    @GetMapping("/getLivingInfoList")
    @ApiOperation(value = "查询正在入住的老人信息模块列表")
    public TableDataInfo<ElderlyPeopleInfoLivingVo> getLivingInfoList(ElderlyPeopleInfoLivingVo elderlyPeopleInfoVo) {
        startPage();
        List<ElderlyPeopleInfoLivingVo> list = elderlyPeopleInfoService.getLivingInfoList(elderlyPeopleInfoVo);
        return getDataTable(list);
    }


    /**
     * 导出老人基础信息列表
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleInfo:export")
    @Log(platform = "1", title = "老人基础信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出老人基础信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ElderlyPeopleInfo elderlyPeopleInfo) {
        List<ElderlyPeopleInfo> list = elderlyPeopleInfoService.selectElderlyPeopleInfoList(elderlyPeopleInfo);
        ExcelUtil<ElderlyPeopleInfo> util = new ExcelUtil<ElderlyPeopleInfo>(ElderlyPeopleInfo.class);
        util.exportExcel(response, list, "老人基础信息数据");
    }

    /**
     * 获取老人基础信息详细信息
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleInfo:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取老人基础信息详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "数据id", required = true, dataTypeClass = String.class)
    })
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(elderlyPeopleInfoService.selectElderlyPeopleInfoById(id));
    }

    /**
     * 新增老人基础信息
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleInfo:add")
    @Log(platform = "1", title = "老人基础信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增老人基础信息")
    @PostMapping
    public AjaxResult add(@RequestBody ElderlyPeopleInfo elderlyPeopleInfo) {
        return AjaxResult.success().put("id", elderlyPeopleInfoService.insertElderlyPeopleInfo(elderlyPeopleInfo));
    }

    /**
     * 修改老人基础信息
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleInfo:edit")
    @Log(platform = "1", title = "老人基础信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改老人基础信息")
    @PutMapping
    public AjaxResult edit(@RequestBody ElderlyPeopleInfo elderlyPeopleInfo) {
        elderlyPeopleInfoService.updateElderlyPeopleInfo(elderlyPeopleInfo);
        return AjaxResult.success().put("id", elderlyPeopleInfo.getId());
    }

    /**
     * 删除老人基础信息
     */
    //@RequiresPermissions("elderlyPeople:elderlyPeopleInfo:remove")
    @Log(platform = "1", title = "老人基础信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除老人基础信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(elderlyPeopleInfoService.logicalDeleteElderlyPeopleInfoByIds(ids));
    }

    @GetMapping("/getUserList")
    @ApiOperation(value = "获取老人全量列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "name", value = "姓名", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "state", value = "入住状态：0入住中，1未入住", required = false, dataTypeClass = String.class),
    })
    public AjaxResult getUserList(@ApiIgnore String name, String state) {
        List<JSONObject> userList = elderlyPeopleInfoService.getUserList(name, state);
        return AjaxResult.success().put("data", userList);
    }

    /**
     * 老人签约（走意向客户直接办理入住的逻辑）
     */
    @ApiOperation(value = "老人签约")
    @PostMapping("/sign")
    public AjaxResult sign(@RequestBody ContractInfo contractInfo) {
        return AjaxResult.success().put("id", elderlyPeopleInfoService.sign(contractInfo));
    }
}
