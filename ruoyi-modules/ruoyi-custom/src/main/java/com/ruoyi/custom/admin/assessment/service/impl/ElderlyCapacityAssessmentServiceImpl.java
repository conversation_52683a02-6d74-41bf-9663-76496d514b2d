package com.ruoyi.custom.admin.assessment.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.custom.admin.assessment.constant.AssessmentItemEnum;
import com.ruoyi.custom.admin.assessment.constant.CapabilityLevelEnum;
import com.ruoyi.custom.admin.assessment.constant.CareRiskEnum;
import com.ruoyi.custom.admin.assessment.domain.AssessmentPlan;
import com.ruoyi.custom.admin.assessment.domain.ElderlyCapacityAssessment;
import com.ruoyi.custom.admin.assessment.domain.NursingPlanTemplate;
import com.ruoyi.custom.admin.assessment.mapper.AssessmentPlanMapper;
import com.ruoyi.custom.admin.assessment.mapper.ElderlyCapacityAssessmentMapper;
import com.ruoyi.custom.admin.assessment.mapper.NursingPlanTemplateMapper;
import com.ruoyi.custom.admin.assessment.service.IElderlyCapacityAssessmentService;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.marketing.domain.MarketingCustomerInfo;
import com.ruoyi.custom.admin.marketing.service.MarketingCustomerInfoService;
import com.ruoyi.custom.utils.DictUtils;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;


/**
 * 老年人能力评估Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Service
public class ElderlyCapacityAssessmentServiceImpl implements IElderlyCapacityAssessmentService {
    @Autowired
    private ElderlyCapacityAssessmentMapper elderlyCapacityAssessmentMapper;

    @Autowired
    private AssessmentPlanMapper assessmentPlanMapper;

    @Autowired
    private NursingPlanTemplateMapper nursingPlanTemplateMapper;

    @Autowired
    private MarketingCustomerInfoService marketingCustomerInfoService;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;


    /**
     * 查询老年人能力评估
     *
     * @param id 老年人能力评估主键
     * @return 老年人能力评估
     */
    @Override
    public ElderlyCapacityAssessment selectElderlyCapacityAssessmentById(Long id) {
        return elderlyCapacityAssessmentMapper.selectElderlyCapacityAssessmentById(id);
    }

    /**
     * 查询老年人能力评估列表
     *
     * @param elderlyCapacityAssessment 老年人能力评估
     * @return 老年人能力评估
     */
    @Override
    public List<ElderlyCapacityAssessment> selectElderlyCapacityAssessmentList(ElderlyCapacityAssessment elderlyCapacityAssessment) {

        return elderlyCapacityAssessmentMapper.selectElderlyCapacityAssessmentList(elderlyCapacityAssessment);
    }

    @Override
    public ElderlyCapacityAssessment selectElderlyCapacityAssessmentBySerialNumber(String serialNumber) {
        return elderlyCapacityAssessmentMapper.selectElderlyCapacityAssessmentBySerialNumber(serialNumber);
    }

    /**
     * 新增老年人能力评估
     *
     * @param elderlyCapacityAssessment 老年人能力评估
     * @return 结果
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertElderlyCapacityAssessment(ElderlyCapacityAssessment elderlyCapacityAssessment) {
        DateTime now = DateTime.now();

        JSONObject assessmentObjBasicsInfo = elderlyCapacityAssessment.getAssessmentObjBasicsInfo();
        if (assessmentObjBasicsInfo != null) {
            assessmentObjBasicsInfo.put("actualStartTime", Optional.ofNullable(assessmentObjBasicsInfo.get("actualStartTime")).orElse(DateUtil.format(now, "yyyy年MM月dd日")).toString()); // xxxx年xx月xx日
        }

        if ((boolean) elderlyCapacityAssessment.getParams().get("finish")) {
            long assessmentID = elderlyCapacityAssessment.getAssessmentId();
            // 如果状态 传过来为2    生成报告  插入到计划表
            JSONObject assessmentResultJson = createAssessmentResultJson(elderlyCapacityAssessment);
            AssessmentPlan assessmentPlan = assessmentPlanMapper.selectAssessmentPlanById(assessmentID);
            if (Objects.isNull(assessmentPlan)) {
                throw new ServiceException("评估计划不存在");
            }
            assessmentPlan.setAssessmentResult(assessmentResultJson);
            assessmentPlan.setActualEndTime(now);
            assessmentPlan.setStatus("2");

            // 更新营销客户基础信息和老人基础信息
            MarketingCustomerInfo marketingCustomerInfo = marketingCustomerInfoService.selectMarketingCustomerInfoById(assessmentPlan.getCustomerId());
            if (marketingCustomerInfo != null) {

                // 更新营销客户信息
                updateMarketingCustomerInfo(marketingCustomerInfo, assessmentObjBasicsInfo);
                marketingCustomerInfoService.updateMarketingCustomerInfo(marketingCustomerInfo);

                // 更新老人基础信息（如果存在）
                if (marketingCustomerInfo.getElderlyId() != null) {
                    ElderlyPeopleInfo elderlyPeopleInfo = elderlyPeopleInfoService.selectElderlyPeopleInfoById(marketingCustomerInfo.getElderlyId());
                    if (elderlyPeopleInfo != null) {
                        updateElderlyPeopleInfo(elderlyPeopleInfo, assessmentObjBasicsInfo);
                        elderlyPeopleInfoService.updateElderlyPeopleInfo(elderlyPeopleInfo);
                    }
                }
            }

            // 生成护理计划指标
            JSONArray healthProblems = elderlyCapacityAssessment.getHealthProblems();
            if (healthProblems != null && !healthProblems.isEmpty()) {
                JSONArray nursingPlanIndicators = new JSONArray();

                // 遍历健康问题，匹配护理计划模板
                for (int i = 0; i < healthProblems.size(); i++) {
                    String healthProblem = healthProblems.getString(i);

                    // 根据健康问题查询匹配的护理计划模板
                    NursingPlanTemplate template = nursingPlanTemplateMapper.selectNursingPlanTemplateByHealthProblem(healthProblem);
                    if (template != null) {
                        // 构建护理计划指标项
                        JSONObject indicator = new JSONObject();
                        indicator.put("uuid", UUID.randomUUID().toString());
                        indicator.put("issueConfirmDate", DateUtil.format(now, "yyyy年MM月dd日")); // 格式：2025年6月29日
                        indicator.put("issueEndDate", ""); // 格式：2025年6月29日
                        indicator.put("problem", healthProblem);
                        indicator.put("details", template.getDetails());

                        nursingPlanIndicators.add(indicator);
                    }
                }

                // 设置护理计划指标
                if (!nursingPlanIndicators.isEmpty()) {
                    assessmentPlan.setNursingPlanIndicators(nursingPlanIndicators);
                }
            }

            assessmentPlanMapper.updateAssessmentPlan(assessmentPlan);
        }

        ElderlyCapacityAssessment elderlyCapacityAssessmentBySerialNumber = elderlyCapacityAssessmentMapper.selectElderlyCapacityAssessmentBySerialNumber(elderlyCapacityAssessment.getSerialNumber());
        if (elderlyCapacityAssessmentBySerialNumber != null) {
            elderlyCapacityAssessment.setId(elderlyCapacityAssessmentBySerialNumber.getId());
            return elderlyCapacityAssessmentMapper.updateElderlyCapacityAssessment(elderlyCapacityAssessment);
        }


        return elderlyCapacityAssessmentMapper.insertElderlyCapacityAssessment(elderlyCapacityAssessment);
    }

    /**
     * 修改老年人能力评估
     *
     * @param elderlyCapacityAssessment 老年人能力评估
     * @return 结果
     */
    @Override
    public int updateElderlyCapacityAssessment(ElderlyCapacityAssessment elderlyCapacityAssessment) {
        return elderlyCapacityAssessmentMapper.updateElderlyCapacityAssessment(elderlyCapacityAssessment);
    }

    /**
     * 批量删除老年人能力评估
     *
     * @param ids 需要删除的老年人能力评估主键
     * @return 结果
     */
    @Override
    public int deleteElderlyCapacityAssessmentByIds(Long[] ids) {
        return elderlyCapacityAssessmentMapper.deleteElderlyCapacityAssessmentByIds(ids);
    }

    /**
     * 删除老年人能力评估信息
     *
     * @param id 老年人能力评估主键
     * @return 结果
     */
    @Override
    public int deleteElderlyCapacityAssessmentById(Long id) {
        return elderlyCapacityAssessmentMapper.deleteElderlyCapacityAssessmentById(id);
    }


    /**
     * 获取评估报告
     *
     * @param elderlyCapacityAssessment
     * @return 结果
     */
    @SneakyThrows
    @Override
    public void getAssessmentReport(ElderlyCapacityAssessment elderlyCapacityAssessment, HttpServletResponse response) {
        /**
         * 获取数据
         */
        Map<String, Object> evaluationReportData = getEvaluationReportData(elderlyCapacityAssessment);

        /**
         * 字典值转换处理
         */
        // 转换评估原因字典值
        if (evaluationReportData.containsKey("assessmentReason") && evaluationReportData.get("assessmentReason") != null) {
            String assessmentReasonValue = evaluationReportData.get("assessmentReason").toString().trim();
            if (!assessmentReasonValue.isEmpty() && !" ".equals(assessmentReasonValue)) {
                String assessmentReasonLabel = DictUtils.selectDictLabel("custom_assessment_plan_reason", assessmentReasonValue);
                if (assessmentReasonLabel != null && !assessmentReasonLabel.isEmpty()) {
                    evaluationReportData.put("assessmentReason", assessmentReasonLabel);
                }
            }
        }

        // 转换民族字典值
        if (evaluationReportData.containsKey("nationality") && evaluationReportData.get("nationality") != null) {
            String nationalityValue = evaluationReportData.get("nationality").toString().trim();
            if (!nationalityValue.isEmpty() && !" ".equals(nationalityValue)) {
                String nationalityLabel = DictUtils.selectDictLabel("nation", nationalityValue);
                if (nationalityLabel != null && !nationalityLabel.isEmpty()) {
                    evaluationReportData.put("nationality", nationalityLabel);
                }
            }
        }

        /**
         * 配置插件，渲染模板
         */
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        Configure config = Configure.builder()
                .bind("medicationSituation", policy)
                .build();

        XWPFTemplate template = XWPFTemplate
                .compile(ResourceUtil.getStream("templates/AssessmentTemplate.docx"), config)
                .render(evaluationReportData);

        /**
         * 设置响应头，触发浏览器下载
         */
        String fileName = "能力评估报告.docx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());

        response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        response.setContentType("application/octet-stream");
        // 使用 'filename*' 提供 utf-8 编码支持
        response.setHeader("Content-disposition", "attachment; filename=" + encodedFileName + ";filename*=utf-8''" + encodedFileName);

        /**
         * 输出到响应流
         */
        try (OutputStream out = response.getOutputStream();
             BufferedOutputStream bos = new BufferedOutputStream(out)) {
            template.write(bos);
            bos.flush();
        }

        // 关闭模板资源
        template.close();
    }


    public List<Map<String, String>> getListMap(JSONArray jsonArray) {
        List<Map<String, String>> list = new ArrayList<>();

        // 添加空值检查
        if (jsonArray == null || jsonArray.isEmpty()) {
            return list;
        }

        for (int i = 0; i < jsonArray.size(); i++) {
            try {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                if (jsonObject != null) {
                    Map<String, String> map = new HashMap<>();
                    for (String key : jsonObject.keySet()) {
                        Object value = jsonObject.get(key);
                        map.put(key, value != null ? value.toString() : "");
                    }
                    list.add(map);
                }
            } catch (Exception e) {
                // 如果某个元素解析失败，跳过该元素，继续处理其他元素
                continue;
            }
        }
        return list;
    }

    public String getStringValue(JSONArray jsonArray) {
        // 添加空值检查
        if (jsonArray == null || jsonArray.isEmpty()) {
            return " ";
        }

        // 转换为逗号分隔的字符串
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < jsonArray.size(); i++) {
            try {
                if (i > 0) {
                    result.append(",");
                }
                Object value = jsonArray.get(i);
                result.append(value != null ? value.toString() : "");
            } catch (Exception e) {
                // 如果某个元素获取失败，跳过该元素
                continue;
            }
        }
        return result.toString();
    }


    /**
     * 生成报告
     *
     * @param elderlyCapacityAssessment
     * @return
     */
    public JSONObject createAssessmentResultJson(ElderlyCapacityAssessment elderlyCapacityAssessment) {
        JSONObject result = new JSONObject();
        JSONObject firstLevelIndicator = new JSONObject();

        int selfCareAbility = Integer.parseInt(elderlyCapacityAssessment.getOldPeopleAbilityAssessment().get("countScore").toString());
        int basicMotorAbility = Integer.parseInt(elderlyCapacityAssessment.getBasicMotorAbilityAssessment().get("countScore").toString());
        int mentalState = Integer.parseInt(elderlyCapacityAssessment.getMentalState().get("countScore").toString());
        int socialParticipation = Integer.parseInt(elderlyCapacityAssessment.getPerceptionSocialParticipation().get("countScore").toString());
        firstLevelIndicator.put("selfCareAbility", selfCareAbility);
        firstLevelIndicator.put("basicMotorAbility", basicMotorAbility);
        firstLevelIndicator.put("mentalState", mentalState);
        firstLevelIndicator.put("socialParticipation", socialParticipation);
        result.put("firstLevelIndicator", firstLevelIndicator);

        int[] numbers = {selfCareAbility, basicMotorAbility, mentalState, socialParticipation};

        result.put("preliminaryGradeScore", Arrays.stream(numbers).sum());
        List initialLevelAbilityElderlyList = new ArrayList();
        if (Arrays.stream(numbers).sum() == 90) {
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.INTACT.getCode());
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.INTACT.getMsg());
            initialLevelAbilityElderlyList.add(Arrays.stream(numbers).sum());
        } else if (Arrays.stream(numbers).sum() >= 66 && Arrays.stream(numbers).sum() <= 89) {
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.MILD.getCode());
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.MILD.getMsg());
            initialLevelAbilityElderlyList.add(Arrays.stream(numbers).sum());
        } else if (Arrays.stream(numbers).sum() >= 46 && Arrays.stream(numbers).sum() <= 65) {
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.MODERATE.getCode());
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.MODERATE.getMsg());
            initialLevelAbilityElderlyList.add(Arrays.stream(numbers).sum());
        } else if (Arrays.stream(numbers).sum() >= 30 && Arrays.stream(numbers).sum() <= 45) {
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.SEVERE.getCode());
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.SEVERE.getMsg());
            initialLevelAbilityElderlyList.add(Arrays.stream(numbers).sum());
        } else if (Arrays.stream(numbers).sum() >= 0 && Arrays.stream(numbers).sum() <= 29) {
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.LOSE.getCode());
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.LOSE.getMsg());
            initialLevelAbilityElderlyList.add(Arrays.stream(numbers).sum());
        }
        result.put("initialLevelAbilityElderly", initialLevelAbilityElderlyList.toArray());

        JSONObject healthRelatedIssues = elderlyCapacityAssessment.getHealthRelatedIssues();
        JSONObject diseaseDiagnosis = elderlyCapacityAssessment.getDiseaseDiagnosisDrugUsage();
        JSONObject assessmentObjBasicsInfo = elderlyCapacityAssessment.getAssessmentObjBasicsInfo();

        JSONArray jsonArray = new JSONArray();


        List<String> ultimately = new ArrayList<>();

        String yes = "有";
        if (yes.equals(healthRelatedIssues.get("coma").toString())) {
            JSONObject basisAbilityLevelChange = new JSONObject();
            ultimately.add("a");
            basisAbilityLevelChange.put("option", "a");
            basisAbilityLevelChange.put("title", "处于昏迷状态者，直接评定为能力完全丧失");
            jsonArray.add(basisAbilityLevelChange);
        }

        JSONObject diseaseDiagnosis1 = diseaseDiagnosis.getJSONObject("diseaseDiagnosis");
        JSONArray fixedArray = diseaseDiagnosis1.getJSONArray("fixed");

        ObjectMapper mapper = new ObjectMapper();
        // 转换为 int 数组
        String[] fixedNumbers = mapper.convertValue(fixedArray, String[].class);
        String fixed = "痴呆F00～F03";
        if (Arrays.stream(fixedNumbers).anyMatch(value -> fixed.equals(value))) {
            JSONObject basisAbilityLevelChange = new JSONObject();
            ultimately.add("b");
            basisAbilityLevelChange.put("option", "b");
            basisAbilityLevelChange.put("title", "确诊为痴呆(F00～F03)、精神科专科医生诊断的其他精神和行为障碍疾病(F04～ F99），在原有能力级别上提高一个等级;");
            jsonArray.add(basisAbilityLevelChange);
        }

        JSONObject careRisksWithinThePast30Days = assessmentObjBasicsInfo.getJSONObject("CareRisksWithinThePast30Days");
        int tumble = Integer.parseInt(careRisksWithinThePast30Days.get("tumble").toString());
        int beLost = Integer.parseInt(careRisksWithinThePast30Days.get("beLost").toString());
        int chokingOnFood = Integer.parseInt(careRisksWithinThePast30Days.get("ChokingOnFood").toString());
        int suicideAndSelfHarm = Integer.parseInt(careRisksWithinThePast30Days.get("SuicideAndSelfHarm").toString());
        int other = Integer.parseInt(careRisksWithinThePast30Days.get("other").toString());

        if (tumble == 2 || tumble == 3 ||
                beLost == 2 || beLost == 3 ||
                chokingOnFood == 2 || chokingOnFood == 3 ||
                suicideAndSelfHarm == 2 || suicideAndSelfHarm == 3 ||
                other == 2 || other == 3
        ) {
            ultimately.add("c");
            JSONObject basisAbilityLevelChange = new JSONObject();
            basisAbilityLevelChange.put("option", "c");
            basisAbilityLevelChange.put("title", "近30天内发生过2次及以上照护风险事件（如跌倒、噎食、自杀、自伤、走失等），在原有能力级别上提高一个等级");
            jsonArray.add(basisAbilityLevelChange);
        }
        result.put("basisAbilityLevelChange", jsonArray);

        List finalLevel = new ArrayList();
        int i = Integer.parseInt(initialLevelAbilityElderlyList.get(0).toString());
        if (ultimately.stream().anyMatch(value -> "a".equals(value))) {
            finalLevel.add(CapabilityLevelEnum.LOSE5.getCode());
            finalLevel.add(CapabilityLevelEnum.LOSE5.getMsg());
            result.put("finalLevel", finalLevel);
        } else if (ultimately.stream().anyMatch(value -> "c".equals(value)) && ultimately.stream().anyMatch(value1 -> "b".equals(value1))) {
            if (i <= 3) {
                CapabilityLevelEnum requiredPoints = CapabilityLevelEnum.getRequiredPoints(i + 2);
                finalLevel.add(requiredPoints.getCode());
                finalLevel.add(requiredPoints.getMsg());
                result.put("finalLevel", finalLevel);
            } else {
                CapabilityLevelEnum requiredPoints = CapabilityLevelEnum.getRequiredPoints(i + 1);
                finalLevel.add(requiredPoints.getCode());
                finalLevel.add(requiredPoints.getMsg());
                result.put("finalLevel", finalLevel);
            }
        } else if (ultimately.stream().anyMatch(value -> "b".equals(value))) {

            if (i != 5) {
                CapabilityLevelEnum requiredPoints = CapabilityLevelEnum.getRequiredPoints(i + 1);
                finalLevel.add(requiredPoints.getCode());
                finalLevel.add(requiredPoints.getMsg());
                result.put("finalLevel", finalLevel);
            }
        } else if (ultimately.stream().anyMatch(value -> "c".equals(value))) {
            if (i != 5) {
                CapabilityLevelEnum requiredPoints = CapabilityLevelEnum.getRequiredPoints(i + 1);
                finalLevel.add(requiredPoints.getCode());
                finalLevel.add(requiredPoints.getMsg());
                result.put("finalLevel", finalLevel);
            }
        }

        return result;
    }

    /**
     * 组装数据
     *
     * @param elderlyCapacityAssessment
     * @return
     */
    public Map<String, Object> getEvaluationReportData(ElderlyCapacityAssessment elderlyCapacityAssessment) {
        Map<String, Object> data = new HashMap<>();

        // 参数校验
        if (elderlyCapacityAssessment == null || elderlyCapacityAssessment.getSerialNumber() == null) {
            throw new IllegalArgumentException("评估对象或序列号不能为空");
        }

        // 获取评估信息
        ElderlyCapacityAssessment assessment = elderlyCapacityAssessmentMapper.selectElderlyCapacityAssessmentBySerialNumber(elderlyCapacityAssessment.getSerialNumber());
        if (assessment == null) {
            throw new IllegalArgumentException("未找到对应的评估记录，序列号：" + elderlyCapacityAssessment.getSerialNumber());
        }

        // 基本信息
        data.put("serialNumber", Optional.ofNullable(assessment.getSerialNumber()).orElse(""));

        // 1. 处理评估对象基本信息 - 包含个人信息、居住情况、护理风险等
        processAssessmentObjBasicsInfo(assessment, data);

        // 2. 处理信息提供者及联系人信息 - 包含提供者姓名、关系、联系方式等
        processMsgSupplierContactInfo(assessment, data);

        // 3. 处理疾病诊断和用药情况 - 包含疾病诊断、用药清单等
        processDiseaseDiagnosisDrugUsage(assessment, data);

        // 4. 处理健康相关问题 - 包含压疮、关节活动、伤口状况、疼痛感觉等
        processHealthRelatedIssues(assessment, data);

        // 5. 处理生理及身体评估 - 包含心脏功能、呼吸功能、意识状态、泌尿系统、皮肤状态等
        processPhysiologyBodyAssessment(assessment, data);

        // 6. 处理老年人能力评估表 - 包含进食、修饰、洗澡、穿衣、大小便控制等日常生活能力
        processOldPeopleAbilityAssessment(assessment, data);

        // 7. 处理基础运动能力评估表 - 包含床椅转移、体位转换、平地行走、上下楼梯等
        processBasicMotorAbilityAssessment(assessment, data);

        // 8. 处理精神状态表 - 包含时间定向、空间定向、人物定向、记忆力、理解力等
        processMentalState(assessment, data);

        // 9. 处理感知觉与社会参与评估表 - 包含视力、听力、日常事务、交通工具使用、社会交往等
        processPerceptionSocialParticipation(assessment, data);

        // 10. 处理健康问题 - 处理健康问题数组数据
        processHealthProblems(assessment, data);

        // 11. 处理评估结果 - 包含自理能力、运动能力、精神状态、社会参与等综合评估结果
        processAssessmentResult(assessment, data);

        return data;
    }

    /**
     * 处理评估对象基本信息
     */
    private void processAssessmentObjBasicsInfo(ElderlyCapacityAssessment assessment, Map<String, Object> data) {
        JSONObject assessmentObjBasicsInfo = assessment.getAssessmentObjBasicsInfo();
        if (assessmentObjBasicsInfo == null) {
            throw new IllegalArgumentException("评估对象基本信息不能为空");
        }

        // 基本时间和原因信息
        data.put("actualStartTime", Optional.ofNullable(assessmentObjBasicsInfo.get("actualStartTime")).orElse(" ").toString());
        data.put("assessmentReason", Optional.ofNullable(assessmentObjBasicsInfo.get("assessmentReason")).orElse(" ").toString());

        // 评估对象基本信息表 - 使用安全的空值处理
        data.put("elderlyName", Optional.ofNullable(assessmentObjBasicsInfo.get("elderlyName")).orElse(" ").toString());
        data.put("sex", Optional.ofNullable(assessmentObjBasicsInfo.get("sex")).orElse(" ").toString());
        data.put("elderlyDateBirth", Optional.ofNullable(assessmentObjBasicsInfo.get("elderlyDateBirth")).orElse(" ").toString());
        data.put("Height", Optional.ofNullable(assessmentObjBasicsInfo.get("height")).orElse(" ").toString());
        data.put("weight", Optional.ofNullable(assessmentObjBasicsInfo.get("weight")).orElse(" ").toString());
        data.put("nationality", Optional.ofNullable(assessmentObjBasicsInfo.get("nationality")).orElse(" ").toString());
        data.put("religion", Optional.ofNullable(assessmentObjBasicsInfo.get("religion")).orElse(" ").toString());
        data.put("ID", Optional.ofNullable(assessmentObjBasicsInfo.get("ID")).orElse(" ").toString());
        data.put("cultureLevel", Optional.ofNullable(assessmentObjBasicsInfo.get("cultureLevel")).orElse(" ").toString());
        data.put("LivingSituation", getStringValue(assessmentObjBasicsInfo.getJSONArray("LivingSituation")));
        data.put("maritalStatus", Optional.ofNullable(assessmentObjBasicsInfo.get("maritalStatus")).orElse(" ").toString());

        // 医疗支付和经济来源
        data.put("MedicalPayment", getStringValue(assessmentObjBasicsInfo.getJSONArray("MedicalPayment")));
        data.put("EconomicSource", getStringValue(assessmentObjBasicsInfo.getJSONArray("EconomicSource")));

        // 护理风险评估
        processCareRisks(assessmentObjBasicsInfo, data);
    }

    /**
     * 处理护理风险评估
     */
    private void processCareRisks(JSONObject assessmentObjBasicsInfo, Map<String, Object> data) {
        JSONObject careRisksWithinThePast30Days = assessmentObjBasicsInfo.getJSONObject("CareRisksWithinThePast30Days");
        if (careRisksWithinThePast30Days != null) {
            data.put("CareRisksWithinThePastTumble",
                    Optional.ofNullable(careRisksWithinThePast30Days.get("tumble"))
                            .map(obj -> CareRiskEnum.getRequiredPoints(Integer.parseInt(obj.toString())).getMsg())
                            .orElse(" "));
            data.put("CareRisksWithinThePastBeLost",
                    Optional.ofNullable(careRisksWithinThePast30Days.get("beLost"))
                            .map(obj -> CareRiskEnum.getRequiredPoints(Integer.parseInt(obj.toString())).getMsg())
                            .orElse(" "));
            data.put("CareRisksWithinThePastChokingOnFood",
                    Optional.ofNullable(careRisksWithinThePast30Days.get("ChokingOnFood"))
                            .map(obj -> CareRiskEnum.getRequiredPoints(Integer.parseInt(obj.toString())).getMsg())
                            .orElse(" "));
            data.put("CareRisksWithinThePastSuicideAndSelfHarm",
                    Optional.ofNullable(careRisksWithinThePast30Days.get("SuicideAndSelfHarm"))
                            .map(obj -> CareRiskEnum.getRequiredPoints(Integer.parseInt(obj.toString())).getMsg())
                            .orElse(" "));
            data.put("CareRisksWithinThePastOther",
                    Optional.ofNullable(careRisksWithinThePast30Days.get("other"))
                            .map(obj -> CareRiskEnum.getRequiredPoints(Integer.parseInt(obj.toString())).getMsg())
                            .orElse(" "));
        } else {
            // 如果护理风险数据为空，设置默认值
            data.put("CareRisksWithinThePastTumble", " ");
            data.put("CareRisksWithinThePastBeLost", " ");
            data.put("CareRisksWithinThePastChokingOnFood", " ");
            data.put("CareRisksWithinThePastSuicideAndSelfHarm", " ");
            data.put("CareRisksWithinThePastOther", " ");
        }
    }

    /**
     * 处理信息提供者及联系人信息
     */
    private void processMsgSupplierContactInfo(ElderlyCapacityAssessment assessment, Map<String, Object> data) {
        JSONObject msgSupplierContactInfo = assessment.getMsgSupplierContactInfo();
        if (msgSupplierContactInfo != null) {
            data.put("providerName", Optional.ofNullable(msgSupplierContactInfo.get("providerName")).orElse(" ").toString());
            data.put("providerRelation", Optional.ofNullable(msgSupplierContactInfo.get("providerRelation")).orElse(" ").toString());
            data.put("ContactPersonName", Optional.ofNullable(msgSupplierContactInfo.get("ContactPersonName")).orElse(" ").toString());
            data.put("ContactPersonTel", Optional.ofNullable(msgSupplierContactInfo.get("ContactPersonTel")).orElse(" ").toString());
        } else {
            data.put("providerName", " ");
            data.put("providerRelation", " ");
            data.put("ContactPersonName", " ");
            data.put("ContactPersonTel", " ");
        }
    }

    /**
     * 处理疾病诊断和用药情况
     */
    private void processDiseaseDiagnosisDrugUsage(ElderlyCapacityAssessment assessment, Map<String, Object> data) {
        JSONObject diseaseDiagnosisDrugUsage = assessment.getDiseaseDiagnosisDrugUsage();
        if (diseaseDiagnosisDrugUsage != null) {
            JSONObject diseaseDiagnosis = diseaseDiagnosisDrugUsage.getJSONObject("diseaseDiagnosis");
            if (diseaseDiagnosis != null) {
                data.put("diseaseDiagnosis", getStringValue(diseaseDiagnosis.getJSONArray("fixed")));
            } else {
                data.put("diseaseDiagnosis", " ");
            }
            data.put("medicationSituation", getListMap(diseaseDiagnosisDrugUsage.getJSONArray("medicationSituation")));
        } else {
            data.put("diseaseDiagnosis", " ");
            data.put("medicationSituation", new ArrayList<>());
        }
    }

    /**
     * 处理健康相关问题
     */
    private void processHealthRelatedIssues(ElderlyCapacityAssessment assessment, Map<String, Object> data) {
        JSONObject healthRelatedIssues = assessment.getHealthRelatedIssues();
        if (healthRelatedIssues != null) {
            data.put("pressureInjury", Optional.ofNullable(healthRelatedIssues.get("pressureInjury")).orElse(" ").toString());
            data.put("motionOfJoint", Optional.ofNullable(healthRelatedIssues.get("motionOfJoint")).orElse(" ").toString());
            data.put("woundCondition", getStringValue(healthRelatedIssues.getJSONArray("woundCondition")));
            data.put("specialCareSituations", getStringValue(healthRelatedIssues.getJSONArray("specialCareSituations")));
            data.put("painSensation", Optional.ofNullable(healthRelatedIssues.get("painSensation")).orElse(" ").toString());
            data.put("toothLossSituation", getStringValue(healthRelatedIssues.getJSONArray("toothLossSituation")));
            data.put("wearingConditionDentures", getStringValue(healthRelatedIssues.getJSONArray("wearingConditionDentures")));
            data.put("situationSymptomsDysphagia", getStringValue(healthRelatedIssues.getJSONArray("situationSymptomsDysphagia")));
            data.put("bodyMassIndex", Optional.ofNullable(healthRelatedIssues.get("bodyMassIndex")).orElse(" ").toString());
            data.put("clearingRespiratoryTractIneffective", Optional.ofNullable(healthRelatedIssues.get("clearingRespiratoryTractIneffective")).orElse(" ").toString());
            data.put("coma", Optional.ofNullable(healthRelatedIssues.get("coma")).orElse(" ").toString());
            data.put("healthRelatedIssuesOther", Optional.ofNullable(healthRelatedIssues.get("other")).orElse(" ").toString());
        } else {
            // 如果健康相关问题数据为空，设置默认值
            data.put("pressureInjury", " ");
            data.put("motionOfJoint", " ");
            data.put("woundCondition", " ");
            data.put("specialCareSituations", " ");
            data.put("painSensation", " ");
            data.put("toothLossSituation", " ");
            data.put("wearingConditionDentures", " ");
            data.put("situationSymptomsDysphagia", " ");
            data.put("bodyMassIndex", " ");
            data.put("clearingRespiratoryTractIneffective", " ");
            data.put("coma", " ");
            data.put("healthRelatedIssuesOther", " ");
        }
    }

    /**
     * 处理生理及身体评估
     */
    private void processPhysiologyBodyAssessment(ElderlyCapacityAssessment assessment, Map<String, Object> data) {
        JSONObject physiologyBodyAssessment = assessment.getPhysiologyBodyAssessment();
        if (physiologyBodyAssessment == null) {
            throw new IllegalArgumentException("生理及身体评估数据不能为空");
        }

        // 心脏功能
        data.put("cardiacFunction", Optional.ofNullable(physiologyBodyAssessment.get("cardiacFunction")).orElse(" ").toString());

        // 呼吸功能
        processRespiratoryFunction(physiologyBodyAssessment, data);

        // 意识状态
        processConsciousState(physiologyBodyAssessment, data);

        // 泌尿系统
        processUrinarySystem(physiologyBodyAssessment, data);

        // 排便习惯
        processDefecationHabit(physiologyBodyAssessment, data);

        // 睡眠形态
        data.put("sleepPattern", Optional.ofNullable(physiologyBodyAssessment.get("sleepPattern")).orElse(" ").toString());

        // 皮肤状态
        processSkinCondition(physiologyBodyAssessment, data);

        // 过敏药物
        processAllergicDrugs(physiologyBodyAssessment, data);

        // 特殊照护
        processSpecialCare(physiologyBodyAssessment, data);

        // 有害习惯
        processHarmfulHabits(physiologyBodyAssessment, data);

        // 肌肉关节
        processJointType(physiologyBodyAssessment, data);

        // 行动力
        data.put("actionAbility", Optional.ofNullable(physiologyBodyAssessment.get("actionAbility")).orElse(" ").toString());

        // 辅具使用
        processAssistiveDevices(physiologyBodyAssessment, data);

        // 跌倒记录
        processFallRecord(physiologyBodyAssessment, data);

        // 疫苗记录
        processVaccinationRecord(physiologyBodyAssessment, data);

        // 疼痛筛查
        processPainScreening(physiologyBodyAssessment, data);
    }

    /**
     * 处理呼吸功能
     */
    private void processRespiratoryFunction(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject respiratoryFunction = physiologyBodyAssessment.getJSONObject("respiratoryFunction");
        if (respiratoryFunction != null) {
            data.put("respiratoryFunction", Optional.ofNullable(respiratoryFunction.get("all")).orElse(" ").toString());
        } else {
            data.put("respiratoryFunction", " ");
        }
    }

    /**
     * 处理意识状态
     */
    private void processConsciousState(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject consciousState = physiologyBodyAssessment.getJSONObject("consciousState");
        if (consciousState != null) {
            String sober = Optional.ofNullable(consciousState.get("sober")).orElse(" ").toString();
            String soberRests = Optional.ofNullable(consciousState.get("soberRests")).orElse(" ").toString();

            if ("清醒".equals(sober)) {
                data.put("GCSTable", false);
            } else {
                data.put("GCSTable", true);
            }
            data.put("eyesOpen", Optional.ofNullable(consciousState.get("eyesOpen")).orElse(" ").toString());
            data.put("bestMotor", Optional.ofNullable(consciousState.get("bestMotor")).orElse(" ").toString());
            data.put("bestVerbalResponse", Optional.ofNullable(consciousState.get("bestVerbalResponse")).orElse(" ").toString());
            data.put("consciousState", sober + soberRests);
        } else {
            data.put("GCSTable", false);
            data.put("eyesOpen", " ");
            data.put("bestMotor", " ");
            data.put("bestVerbalResponse", " ");
            data.put("consciousState", " ");
        }
    }

    /**
     * 处理泌尿系统
     */
    private void processUrinarySystem(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject urinarySystem = physiologyBodyAssessment.getJSONObject("urinarySystem");
        StringBuffer urinarySystemBuffer = new StringBuffer();

        if (urinarySystem != null) {
            String option = Optional.ofNullable(urinarySystem.get("option")).orElse("").toString().trim();
            String night = Optional.ofNullable(urinarySystem.get("night")).orElse("").toString().trim();
            String daytime = Optional.ofNullable(urinarySystem.get("daytime")).orElse("").toString().trim();
            String prostatomegaly = Optional.ofNullable(urinarySystem.get("prostatomegaly")).orElse("").toString().trim();
            String optionRests = Optional.ofNullable(urinarySystem.get("optionRests")).orElse("").toString().trim();
            String urinarySystemAssist = Optional.ofNullable(urinarySystem.get("urinarySystemAssist")).orElse("").toString().trim();
            String changeTheDate = Optional.ofNullable(urinarySystem.get("changeTheDate")).orElse("").toString().trim();
            String currentUrologicalUsage = Optional.ofNullable(urinarySystem.get("currentUrologicalUsage")).orElse("").toString().trim();
            String currentUrologicalUsageRests = Optional.ofNullable(urinarySystem.get("currentUrologicalUsageRests")).orElse("").toString().trim();

            // 添加选项信息
            if (!option.isEmpty()) {
                urinarySystemBuffer.append(option);
                urinarySystemBuffer.append("\n");
            }

            // 添加排尿频率信息
            if (!night.isEmpty() && !daytime.isEmpty()) {
                String frequentUurination = "白天" + daytime + "次   夜晚" + night + "次   \n";
                urinarySystemBuffer.append(frequentUurination);
            }

            // 添加前列腺肥大信息
            if (!prostatomegaly.isEmpty()) {
                urinarySystemBuffer.append(prostatomegaly);
            }

            // 添加其他选项信息
            if (!optionRests.isEmpty()) {
                urinarySystemBuffer.append(optionRests + "\n");
            }

            // 添加辅助用物信息
            if (!urinarySystemAssist.isEmpty() || !changeTheDate.isEmpty()) {
                urinarySystemBuffer.append("辅助用物：" + urinarySystemAssist + "Fr.（近期更换日期 :" + changeTheDate + "）\n");
            }

            // 添加目前使用信息
            if (!currentUrologicalUsage.isEmpty() || !currentUrologicalUsageRests.isEmpty()) {
                urinarySystemBuffer.append("目前使用：" + currentUrologicalUsage + currentUrologicalUsageRests + "\n");
            }

            // 处理高危险性泌尿道感染信息
            String isHighRiskUrinaryTractInfection = Optional.ofNullable(urinarySystem.get("isHighRiskUrinaryTractInfection")).orElse("").toString().trim();
            if ("是".equals(isHighRiskUrinaryTractInfection)) {
                String indwellingCatheter = Optional.ofNullable(urinarySystem.get("indwellingCatheter")).orElse("").toString().trim();
                if (!indwellingCatheter.isEmpty()) {
                    urinarySystemBuffer.append("高危险性泌尿道感染：○性别：女性   ○疾病:糖尿病、肾脏病、尿失禁及反复泌尿道感染   ○留置导尿管 " + indwellingCatheter + "月 ○解尿疼痛或腰背酸痛症状");
                } else {
                    urinarySystemBuffer.append("高危险性泌尿道感染：○性别：女性   ○疾病:糖尿病、肾脏病、尿失禁及反复泌尿道感染   ○留置导尿管 ○解尿疼痛或腰背酸痛症状");
                }
            }
        } else {
            urinarySystemBuffer.append(" ");
        }
        data.put("urinarySystem", urinarySystemBuffer);
    }

    /**
     * 处理排便习惯
     */
    private void processDefecationHabit(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject defecationHdabit = physiologyBodyAssessment.getJSONObject("defecationHdabit");
        StringBuffer defecationHdabitBuffer = new StringBuffer();

        if (defecationHdabit != null) {
            String bowelHabits = Optional.ofNullable(defecationHdabit.get("bowelHabits")).orElse(" ").toString();

            if ("失禁".equals(bowelHabits)) {
                String incontinenceDays = Optional.ofNullable(defecationHdabit.get("incontinenceDays")).orElse(" ").toString();
                defecationHdabitBuffer.append(bowelHabits + ":" + incontinenceDays + "天");
            } else {
                String bowelHabitsDays = Optional.ofNullable(defecationHdabit.get("bowelHabitsDays")).orElse(" ").toString();
                String bowelHabitsFrequency = Optional.ofNullable(defecationHdabit.get("bowelHabitsFrequency")).orElse(" ").toString();
                defecationHdabitBuffer.append(bowelHabits + bowelHabitsDays + "天 " + bowelHabitsFrequency + "   次");
            }
            if (!"正常".equals(bowelHabits)) {
                String bowelHabitsAssistanceMethod = Optional.ofNullable(defecationHdabit.get("bowelHabitsAssistanceMethod")).orElse(" ").toString();
                defecationHdabitBuffer.append("辅助方式：" + bowelHabitsAssistanceMethod);
            }
        } else {
            defecationHdabitBuffer.append(" ");
        }
        data.put("defecationHdabit", defecationHdabitBuffer);
    }

    /**
     * 处理皮肤状态
     */
    private void processSkinCondition(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject skinCondition = physiologyBodyAssessment.getJSONObject("skinCondition");

        if (skinCondition != null) {
            String isSkinCondition = Optional.ofNullable(skinCondition.get("isSkinCondition")).orElse(" ").toString();
            if ("正常".equals(isSkinCondition)) {
                data.put("skinConditionTable", false);
            } else {
                data.put("skinConditionTable", true);
                processBradenScale(skinCondition, data);
            }
            processSkinConditionDetails(skinCondition, data);
        } else {
            data.put("skinConditionTable", false);
            data.put("skinCondition", " ");
        }
    }

    /**
     * 处理Braden量表
     */
    private void processBradenScale(JSONObject skinCondition, Map<String, Object> data) {
        JSONObject bradenScale = skinCondition.getJSONObject("bradenScale");

        if (bradenScale != null) {
            data.put("pressureMobility",
                    Optional.ofNullable(bradenScale.get("pressureMobility"))
                            .map(obj -> AssessmentItemEnum.PRESSURE_MOBILITY.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("pressureActivity",
                    Optional.ofNullable(bradenScale.get("pressureActivity"))
                            .map(obj -> AssessmentItemEnum.PRESSURE_ACTIVITY.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("pressureSkinMoisture",
                    Optional.ofNullable(bradenScale.get("pressureSkinMoisture"))
                            .map(obj -> AssessmentItemEnum.PRESSURE_SKIN_MOISTURE.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("pressureSoreSensation",
                    Optional.ofNullable(bradenScale.get("pressureSoreSensation"))
                            .map(obj -> AssessmentItemEnum.PRESSURE_SORE_SENSATION.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("pressureNutritionStatus",
                    Optional.ofNullable(bradenScale.get("pressureNutritionStatus"))
                            .map(obj -> AssessmentItemEnum.PRESSURE_NUTRITION_STATUS.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("pressureFrictionAndShear",
                    Optional.ofNullable(bradenScale.get("pressureFrictionAndShear"))
                            .map(obj -> AssessmentItemEnum.PRESSURE_FRICTION_AND_SHEAR.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("bradenScaleCountScore", Optional.ofNullable(bradenScale.get("bradenScaleCountScore")).orElse(" ").toString() + "分");
        } else {
            // bradenScale 为空时设置默认值
            data.put("pressureMobility", " ");
            data.put("pressureActivity", " ");
            data.put("pressureSkinMoisture", " ");
            data.put("pressureSoreSensation", " ");
            data.put("pressureNutritionStatus", " ");
            data.put("pressureFrictionAndShear", " ");
            data.put("bradenScaleCountScore", " ");
        }
    }

    /**
     * 处理皮肤状态详细信息
     */
    private void processSkinConditionDetails(JSONObject skinCondition, Map<String, Object> data) {
        StringBuffer skinConditionBuffer = new StringBuffer();
        String isSkinCondition = Optional.ofNullable(skinCondition.get("isSkinCondition")).orElse(" ").toString();
        skinConditionBuffer.append(isSkinCondition);
        skinConditionBuffer.append(Optional.ofNullable(skinCondition.get("scarCause")).orElse(" ").toString());

        if ("疥疮".equals(isSkinCondition)) {
            skinConditionBuffer.append("曾经感染日期:" + Optional.ofNullable(skinCondition.get("scabiesInfectionDate")).orElse(" ").toString()
                    + "\t部位：  " + Optional.ofNullable(skinCondition.get("scarLocation")).orElse(" ").toString() +
                    " \t程度：  " + Optional.ofNullable(skinCondition.get("scarSeverity")).orElse(" ").toString() +
                    "  类别：    " + Optional.ofNullable(skinCondition.get("scarType")).orElse(" ").toString() +
                    "\n");
        }
        skinConditionBuffer.append("\n发量:" + Optional.ofNullable(skinCondition.get("hairVolume")).orElse(" ").toString());
        skinConditionBuffer.append("\n指/趾甲:" + Optional.ofNullable(skinCondition.get("nails")).orElse(" ").toString());
        skinConditionBuffer.append("\n疑似受虐：" + Optional.ofNullable(skinCondition.get("suspectedAbuse")).orElse(" ").toString());
        skinConditionBuffer.append("\n(皮肤外观有多处疑似被打过的新旧伤淤青疤痕，见到陌生人靠近深情惊恐，甚至拒绝外人碰触,如有上诉情况，通知社工人员处理）");

        skinConditionBuffer.append("伤口类别：" + Optional.ofNullable(skinCondition.get("typeOfWound")).orElse(" ").toString() + "\n");

        String isPressureSore = Optional.ofNullable(skinCondition.get("isPressureSore")).orElse(" ").toString();
        if ("是".equals(isPressureSore)) {
            skinConditionBuffer.append("\n压疮（等级：  " + Optional.ofNullable(skinCondition.get("pressureSoreGrade")).orElse(" ").toString() + "    ）\n");
        }

        String otherChronicWounds = Optional.ofNullable(skinCondition.get("otherChronicWounds")).orElse(" ").toString();
        if ("是".equals(otherChronicWounds)) {
            skinConditionBuffer.append("其他慢性伤口\t" +
                    "部位：   " + Optional.ofNullable(skinCondition.get("otherChronicWoundsLocation")).orElse(" ").toString() + "      大小：" +
                    Optional.ofNullable(skinCondition.get("otherChronicWoundsSize")).orElse(" ").toString() + "\n");
        }
        data.put("skinCondition", skinConditionBuffer);
    }

    /**
     * 处理过敏药物
     */
    private void processAllergicDrugs(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject allergicDrugs = physiologyBodyAssessment.getJSONObject("allergicDrugs");
        if (allergicDrugs != null) {
            String allergyMedication = Optional.ofNullable(allergicDrugs.get("allergyMedication")).orElse(" ").toString();
            if ("有".equals(allergyMedication)) {
                data.put("allergicDrugs", allergyMedication
                        + ",药名：" + Optional.ofNullable(allergicDrugs.get("allergyMedicationName")).orElse(" ").toString()
                        + "\t食物：" + Optional.ofNullable(allergicDrugs.get("allergyFoods")).orElse(" ").toString()
                );
            } else {
                data.put("allergicDrugs", allergyMedication);
            }
        } else {
            data.put("allergicDrugs", " ");
        }
    }

    /**
     * 处理特殊照护
     */
    private void processSpecialCare(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject whetherReceiveSpecialCare = physiologyBodyAssessment.getJSONObject("whetherReceiveSpecialCare");

        if (whetherReceiveSpecialCare != null) {
            String isSpecialCare = Optional.ofNullable(whetherReceiveSpecialCare.get("isSpecialCare")).orElse(" ").toString();
            data.put("whetherReceiveSpecialCare", isSpecialCare);
            if ("是".equals(isSpecialCare)) {
                data.put("whetherReceiveSpecialCareTable", true);
                data.put("pipelineName", Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineName")).orElse(" ").toString() +
                        Optional.ofNullable(whetherReceiveSpecialCare.get("pressureSoreGrade")).orElse(" ").toString() +
                        Optional.ofNullable(whetherReceiveSpecialCare.get("artificialBloodVessel")).orElse(" ").toString()
                );
                data.put("pipelineNameDate",
                        Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineNameStartDate")).orElse(" ").toString() + "至" +
                                Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineNameEndDate")).orElse(" ").toString()
                );

                StringBuffer pipelineRestsNameBuffer = new StringBuffer();
                pipelineRestsNameBuffer.append(Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineRestsName")).orElse(" ").toString());

                if ("血液透析".equals(Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineRestsName")).orElse(" ").toString())) {
                    pipelineRestsNameBuffer.append(Optional.ofNullable(whetherReceiveSpecialCare.get("hematodialysis")).orElse(" ").toString() + "次/星期");
                } else if ("氧气治疗".equals(Optional.ofNullable(whetherReceiveSpecialCare.get("oxygenTherapy")).orElse(" ").toString())) {
                    pipelineRestsNameBuffer.append(Optional.ofNullable(whetherReceiveSpecialCare.get("oxygenTherapy")).orElse(" ").toString() + "L/min");
                }
                data.put("pipelineRestsName", pipelineRestsNameBuffer);
                data.put("pipelineRestsNameDate", Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineRestsNameStartDate")).orElse(" ").toString() + "至" +
                        Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineRestsNameEndDate")).orElse(" ").toString());
            } else {
                data.put("whetherReceiveSpecialCareTable", false);
                data.put("whetherReceiveSpecialCare", "否");
            }
        } else {
            data.put("whetherReceiveSpecialCare", " ");
            data.put("whetherReceiveSpecialCareTable", false);
        }
    }

    /**
     * 处理有害习惯
     */
    private void processHarmfulHabits(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject harmfulHabits = physiologyBodyAssessment.getJSONObject("harmfulHabits");
        if (harmfulHabits != null) {
            String isHarmfulHabits = Optional.ofNullable(harmfulHabits.get("isHarmfulHabits")).orElse(" ").toString();

            if ("有".equals(isHarmfulHabits)) {
                StringBuffer harmfulHabitsBuffer = new StringBuffer();
                harmfulHabitsBuffer.append(getStringValue(harmfulHabits.getJSONArray("harmfulHabitsList")) + "\n");
                // 处理吸烟信息
                String dailyCigaretteAmount = Optional.ofNullable(harmfulHabits.get("dailyCigaretteAmount")).orElse("").toString().trim();
                String yearsOfSmoking = Optional.ofNullable(harmfulHabits.get("yearsOfSmoking")).orElse("").toString().trim();
                if (!dailyCigaretteAmount.isEmpty() || !yearsOfSmoking.isEmpty()) {
                    harmfulHabitsBuffer.append(dailyCigaretteAmount + "包/天,已吸" + yearsOfSmoking + "年\n");
                }

                // 处理饮酒信息
                String dailyAlcoholIntake = Optional.ofNullable(harmfulHabits.get("dailyAlcoholIntake")).orElse("").toString().trim();
                String yearsOfDrinking = Optional.ofNullable(harmfulHabits.get("yearsOfDrinking")).orElse("").toString().trim();
                if (!dailyAlcoholIntake.isEmpty() || !yearsOfDrinking.isEmpty()) {
                    harmfulHabitsBuffer.append(dailyAlcoholIntake + " /天，已喝" + yearsOfDrinking + "年\n");
                }

                // 处理其他不良习惯
                String otherHarmfulHabits = Optional.ofNullable(harmfulHabits.get("otherHarmfulHabits")).orElse("").toString().trim();
                if (!otherHarmfulHabits.isEmpty()) {
                    harmfulHabitsBuffer.append(otherHarmfulHabits);
                }
                data.put("harmfulHabits", harmfulHabitsBuffer);
            } else {
                data.put("harmfulHabits", isHarmfulHabits);
            }
        } else {
            data.put("harmfulHabits", " ");
        }
    }

    /**
     * 处理肌肉关节型态
     */
    private void processJointType(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject jointType = physiologyBodyAssessment.getJSONObject("jointType");
        if (jointType != null) {
            data.put("leftRightJointType", "左：" + Optional.ofNullable(jointType.get("leftJointType")).orElse(" ").toString() + "\n右：" +
                    Optional.ofNullable(jointType.get("rightJointType")).orElse(" ").toString());
            data.put("leftMuscleStrength", "左：" + Optional.ofNullable(jointType.get("leftMuscleStrength")).orElse(" ").toString() + "\n右：" +
                    Optional.ofNullable(jointType.get("rightMuscleStrength")).orElse(" ").toString());
            data.put("MuscleAppearanceType", "左：" + Optional.ofNullable(jointType.get("leftMuscleAppearanceType")).orElse(" ").toString() + "\n右：" +
                    Optional.ofNullable(jointType.get("rightMuscleAppearanceType")).orElse(" ").toString());
            String paralysis = Optional.ofNullable(jointType.get("paralysis")).orElse(" ").toString();
            if ("有".equals(paralysis)) {
                data.put("jointType", paralysis + "\t" + Optional.ofNullable(jointType.get("paralysisType")).orElse(" ").toString());
            } else {
                data.put("jointType", paralysis);
            }
        } else {
            data.put("leftRightJointType", " ");
            data.put("leftMuscleStrength", " ");
            data.put("MuscleAppearanceType", " ");
            data.put("jointType", " ");
        }
    }

    /**
     * 处理辅具使用
     */
    private void processAssistiveDevices(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject whetherAssistiveDevices = physiologyBodyAssessment.getJSONObject("whetherAssistiveDevices");

        if (whetherAssistiveDevices != null) {
            String isUsingAssistiveDevices = Optional.ofNullable(whetherAssistiveDevices.get("isUsingAssistiveDevices")).orElse(" ").toString();

            if ("1".equals(isUsingAssistiveDevices)) {
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(getStringValue(whetherAssistiveDevices.getJSONArray("mainAids")) + "\n");
                String isUsingVisionAssistiveDevices = Optional.ofNullable(whetherAssistiveDevices.get("isUsingVisionAssistiveDevices")).orElse(" ").toString();
                String needOtherAssistiveDevices = Optional.ofNullable(whetherAssistiveDevices.get("needOtherAssistiveDevices")).orElse(" ").toString();
                if ("1".equals(isUsingVisionAssistiveDevices)) {
                    stringBuffer.append("视力辅具：" + Optional.ofNullable(whetherAssistiveDevices.get("visionAids")).orElse(" ").toString() + "\n");
                }
                if ("1".equals(needOtherAssistiveDevices)) {
                    stringBuffer.append("其他:" + Optional.ofNullable(whetherAssistiveDevices.get("otherAssistiveDevices")).orElse(" ").toString());
                }
                data.put("whetherAssistiveDevices", stringBuffer);
            } else {
                data.put("whetherAssistiveDevices", isUsingAssistiveDevices);
            }
        } else {
            data.put("whetherAssistiveDevices", " ");
        }
    }

    /**
     * 处理跌倒记录
     */
    private void processFallRecord(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject fallRecord = physiologyBodyAssessment.getJSONObject("fallRecord");
        String fallRecordList = " ";

        if (fallRecord != null) {
            fallRecordList = getStringValue(fallRecord.getJSONArray("fallRecordList"));
            if (Optional.ofNullable(fallRecord.get("fallHistory")).isPresent() && Optional.ofNullable(fallRecord.get("fallFrequency")).isPresent()) {
                String fallHistory = Optional.ofNullable(fallRecord.get("fallHistory")).orElse("").toString();
                if ("1".equals(fallHistory)) {
                    fallHistory = "是";
                } else {
                    fallHistory = "否";
                }
                String fallFrequency = Optional.ofNullable(fallRecord.get("fallFrequency")).orElse("").toString();
                fallRecordList = fallRecordList + "\n近三个月内个案是否有跌倒纪录:" + fallHistory + "\n跌倒发生次数：" + fallFrequency;
            }
        }
        data.put("fallRecord", fallRecordList);
    }

    /**
     * 处理疫苗记录
     */
    private void processVaccinationRecord(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject vaccinationRecord = physiologyBodyAssessment.getJSONObject("vaccinationRecord");
        StringBuffer vaccinationRecordBuffer = new StringBuffer();

        if (vaccinationRecord != null) {
            String fluVaccine = Optional.ofNullable(vaccinationRecord.get("fluVaccine")).orElse(" ").toString();
            String fluVaccineYear = Optional.ofNullable(vaccinationRecord.get("fluVaccineYear")).orElse(" ").toString();
            String pneumococcalVaccine = Optional.ofNullable(vaccinationRecord.get("pneumococcalVaccine")).orElse(" ").toString();
            String pneumococcalVaccineYear = Optional.ofNullable(vaccinationRecord.get("pneumococcalVaccineYear")).orElse(" ").toString();

            if ("有".equals(fluVaccine)) {
                vaccinationRecordBuffer.append("流感疫苗：" + fluVaccine + "施打年度：" + fluVaccineYear + "\n");
            } else {
                vaccinationRecordBuffer.append("流感疫苗：" + fluVaccine + "\n");
            }
            if ("有".equals(pneumococcalVaccine)) {
                vaccinationRecordBuffer.append("肺炎链球菌多糖体疫苗：" + pneumococcalVaccine + "施打年度：" + pneumococcalVaccineYear + "\n");
            } else {
                vaccinationRecordBuffer.append("肺炎链球菌多糖体疫苗：" + pneumococcalVaccine + "\n");
            }
        } else {
            vaccinationRecordBuffer.append(" ");
        }
        data.put("vaccinationRecord", vaccinationRecordBuffer);
    }

    /**
     * 处理疼痛筛查
     */
    private void processPainScreening(JSONObject physiologyBodyAssessment, Map<String, Object> data) {
        JSONObject painScreening = physiologyBodyAssessment.getJSONObject("painScreening");

        if (painScreening != null) {
            String recentPain = Optional.ofNullable(painScreening.get("recentPain")).orElse(" ").toString();
            if ("1".equals(recentPain)) {
                data.put("painScreeningTable", true);

                data.put("comforting",
                        Optional.ofNullable(painScreening.get("comforting"))
                                .map(obj -> AssessmentItemEnum.COMFORTING.getScoreDescription(Integer.parseInt(obj.toString())))
                                .orElse(" "));
                data.put("crying",
                        Optional.ofNullable(painScreening.get("crying"))
                                .map(obj -> AssessmentItemEnum.CRYING.getScoreDescription(Integer.parseInt(obj.toString())))
                                .orElse(" "));
                data.put("painDuringActivity",
                        Optional.ofNullable(painScreening.get("painDuringActivity"))
                                .map(obj -> AssessmentItemEnum.PAINDURINGACTIVITY.getScoreDescription(Integer.parseInt(obj.toString())))
                                .orElse(" "));
                data.put("footPain",
                        Optional.ofNullable(painScreening.get("footPain"))
                                .map(obj -> AssessmentItemEnum.FOOTPAIN.getScoreDescription(Integer.parseInt(obj.toString())))
                                .orElse(" "));
                data.put("facePain",
                        Optional.ofNullable(painScreening.get("facePain"))
                                .map(obj -> AssessmentItemEnum.FACEPAIN.getScoreDescription(Integer.parseInt(obj.toString())))
                                .orElse(" "));
                data.put("painScreeningTotal", Optional.ofNullable(painScreening.get("painScreeningTotal")).orElse(" ").toString());

                StringBuffer painScreeningBuffer = new StringBuffer();
                painScreeningBuffer.append("引发因素：" + Optional.ofNullable(painScreening.get("triggerFactors")).orElse(" ").toString() + Optional.ofNullable(painScreening.get("otherPainTriggerCauses")).orElse(" ").toString());
                painScreeningBuffer.append("\n性质:" + Optional.ofNullable(painScreening.get("nature")).orElse(" ").toString());
                painScreeningBuffer.append("\n部位:" + Optional.ofNullable(painScreening.get("locationOfPain")).orElse(" ").toString() + "(" + Optional.ofNullable(painScreening.get("referredToOtherLocations")).orElse(" ").toString() + ")");
                painScreeningBuffer.append("\n程度：" + Optional.ofNullable(painScreening.get("painDegree")).orElse(" ").toString());
                painScreeningBuffer.append("\n持续时间：" + Optional.ofNullable(painScreening.get("painDuration")).orElse(" ").toString() + "\t" + "(" + Optional.ofNullable(painScreening.get("painDurationRest")).orElse(" ").toString() + ")");

                String takingPainkillers = Optional.ofNullable(painScreening.get("takingPainkillers")).orElse(" ").toString();
                if ("1".equals(takingPainkillers)) {
                    takingPainkillers = "有";
                    painScreeningBuffer.append("\n使用止疼药：：" + takingPainkillers + ",药名：" + Optional.ofNullable(painScreening.get("usingPainkillerName")).orElse(" "));
                } else {
                    takingPainkillers = "无";
                    painScreeningBuffer.append("\n使用止疼药：：" + takingPainkillers);
                }

                if (Optional.ofNullable(painScreening.get("currentPain")).isPresent()) {
                    painScreeningBuffer.append("\n目前疼痛：" + Optional.ofNullable(painScreening.get("currentPain")).orElse(" ").toString());
                }
                data.put("painScreening", painScreeningBuffer);
            } else {
                data.put("painScreeningTable", false);
                data.put("painScreening", "否");
            }
        } else {
            data.put("painScreeningTable", false);
            data.put("painScreening", " ");
        }
    }

    /**
     * 处理老年人能力评估表
     */
    private void processOldPeopleAbilityAssessment(ElderlyCapacityAssessment assessment, Map<String, Object> data) {
        JSONObject oldPeopleAbilityAssessment = assessment.getOldPeopleAbilityAssessment();
        if (oldPeopleAbilityAssessment != null) {
            data.put("eating",
                    Optional.ofNullable(oldPeopleAbilityAssessment.get("eating"))
                            .map(obj -> AssessmentItemEnum.EATING.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("modification",
                    Optional.ofNullable(oldPeopleAbilityAssessment.get("modification"))
                            .map(obj -> AssessmentItemEnum.GROOMING.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("bath",
                    Optional.ofNullable(oldPeopleAbilityAssessment.get("bath"))
                            .map(obj -> AssessmentItemEnum.BATHING.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("takeOffCoat",
                    Optional.ofNullable(oldPeopleAbilityAssessment.get("takeOffCoat"))
                            .map(obj -> AssessmentItemEnum.UPPER_BODY_DRESSING.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("shoesSocks",
                    Optional.ofNullable(oldPeopleAbilityAssessment.get("shoesSocks"))
                            .map(obj -> AssessmentItemEnum.LOWER_BODY_DRESSING.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("urinaryControl",
                    Optional.ofNullable(oldPeopleAbilityAssessment.get("urinaryControl"))
                            .map(obj -> AssessmentItemEnum.URINARY_CONTROL.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("stoolControl",
                    Optional.ofNullable(oldPeopleAbilityAssessment.get("stoolControl"))
                            .map(obj -> AssessmentItemEnum.BOWEL_CONTROL.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("toTheToilet",
                    Optional.ofNullable(oldPeopleAbilityAssessment.get("toTheToilet"))
                            .map(obj -> AssessmentItemEnum.TOILETING.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
        } else {
            // 设置默认值
            data.put("eating", " ");
            data.put("modification", " ");
            data.put("bath", " ");
            data.put("takeOffCoat", " ");
            data.put("shoesSocks", " ");
            data.put("urinaryControl", " ");
            data.put("stoolControl", " ");
            data.put("toTheToilet", " ");
        }
    }

    /**
     * 处理基础运动能力评估表
     */
    private void processBasicMotorAbilityAssessment(ElderlyCapacityAssessment assessment, Map<String, Object> data) {
        JSONObject basicMotorAbilityAssessment = assessment.getBasicMotorAbilityAssessment();
        if (basicMotorAbilityAssessment != null) {
            data.put("bedChairTransfer",
                    Optional.ofNullable(basicMotorAbilityAssessment.get("bedChairTransfer"))
                            .map(obj -> AssessmentItemEnum.BED_TRANSFER.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("bedPositionTransfer",
                    Optional.ofNullable(basicMotorAbilityAssessment.get("bedPositionTransfer"))
                            .map(obj -> AssessmentItemEnum.CHAIR_TRANSFER.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("walkingGround",
                    Optional.ofNullable(basicMotorAbilityAssessment.get("walkingGround"))
                            .map(obj -> AssessmentItemEnum.WALKING.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("upDownStairs",
                    Optional.ofNullable(basicMotorAbilityAssessment.get("upDownStairs"))
                            .map(obj -> AssessmentItemEnum.STAIRS.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
        } else {
            data.put("bedChairTransfer", " ");
            data.put("bedPositionTransfer", " ");
            data.put("walkingGround", " ");
            data.put("upDownStairs", " ");
        }
    }

    /**
     * 处理精神状态表
     */
    private void processMentalState(ElderlyCapacityAssessment assessment, Map<String, Object> data) {
        JSONObject mentalState = assessment.getMentalState();
        if (mentalState != null) {
            data.put("timeOrientation",
                    Optional.ofNullable(mentalState.get("timeOrientation"))
                            .map(obj -> AssessmentItemEnum.TIME_ORIENTATION.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("spatialOrientation",
                    Optional.ofNullable(mentalState.get("spatialOrientation"))
                            .map(obj -> AssessmentItemEnum.SPACE_ORIENTATION.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("personalOrientation",
                    Optional.ofNullable(mentalState.get("personalOrientation"))
                            .map(obj -> AssessmentItemEnum.PERSON_ORIENTATION.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("memory",
                    Optional.ofNullable(mentalState.get("memory"))
                            .map(obj -> AssessmentItemEnum.MEMORY.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("savvy",
                    Optional.ofNullable(mentalState.get("savvy"))
                            .map(obj -> AssessmentItemEnum.COMPREHENSION.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("expressionAbility",
                    Optional.ofNullable(mentalState.get("expressionAbility"))
                            .map(obj -> AssessmentItemEnum.EXPRESSION.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("aggressiveBehaviour",
                    Optional.ofNullable(mentalState.get("aggressiveBehaviour"))
                            .map(obj -> AssessmentItemEnum.AGGRESSIVE_BEHAVIOR.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("depressed",
                    Optional.ofNullable(mentalState.get("depressed"))
                            .map(obj -> AssessmentItemEnum.DEPRESSION.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("levelConsciousness",
                    Optional.ofNullable(mentalState.get("levelConsciousness"))
                            .map(obj -> AssessmentItemEnum.CONSCIOUSNESS.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
        } else {
            data.put("timeOrientation", " ");
            data.put("spatialOrientation", " ");
            data.put("personalOrientation", " ");
            data.put("memory", " ");
            data.put("savvy", " ");
            data.put("expressionAbility", " ");
            data.put("aggressiveBehaviour", " ");
            data.put("depressed", " ");
            data.put("levelConsciousness", " ");
        }
    }

    /**
     * 处理感知觉与社会参与评估表
     */
    private void processPerceptionSocialParticipation(ElderlyCapacityAssessment assessment, Map<String, Object> data) {
        JSONObject perceptionSocialParticipation = assessment.getPerceptionSocialParticipation();
        if (perceptionSocialParticipation != null) {
            data.put("vision",
                    Optional.ofNullable(perceptionSocialParticipation.get("vision"))
                            .map(obj -> AssessmentItemEnum.VISION.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("hearing",
                    Optional.ofNullable(perceptionSocialParticipation.get("hearing"))
                            .map(obj -> AssessmentItemEnum.HEARING.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("dailyAffairs",
                    Optional.ofNullable(perceptionSocialParticipation.get("dailyAffairs"))
                            .map(obj -> AssessmentItemEnum.DAILY_AFFAIRS.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("vehicle",
                    Optional.ofNullable(perceptionSocialParticipation.get("vehicle"))
                            .map(obj -> AssessmentItemEnum.TRANSPORTATION.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
            data.put("interactionAbility",
                    Optional.ofNullable(perceptionSocialParticipation.get("interactionAbility"))
                            .map(obj -> AssessmentItemEnum.SOCIAL_INTERACTION.getScoreDescription(Integer.parseInt(obj.toString())))
                            .orElse(" "));
        } else {
            data.put("vision", " ");
            data.put("hearing", " ");
            data.put("dailyAffairs", " ");
            data.put("vehicle", " ");
            data.put("interactionAbility", " ");
        }
    }

    /**
     * 处理健康问题
     */
    private void processHealthProblems(ElderlyCapacityAssessment assessment, Map<String, Object> data) {
        JSONArray healthProblems = assessment.getHealthProblems();
        data.put("healthProblems", getStringValue(healthProblems));
    }

    /**
     * 处理评估结果
     */
    private void processAssessmentResult(ElderlyCapacityAssessment assessment, Map<String, Object> data) {
        AssessmentPlan assessmentPlan = assessmentPlanMapper.selectAssessmentPlanById(assessment.getAssessmentId());
        if (assessmentPlan != null && assessmentPlan.getAssessmentResult() != null) {
            JSONObject assessmentResult = assessmentPlan.getAssessmentResult();

            // 处理一级指标
            processFirstLevelIndicator(assessmentResult, data);

            // 处理评估分数和等级
            data.put("preliminaryGradeScore", Optional.ofNullable(assessmentResult.get("preliminaryGradeScore")).orElse(" ").toString());

            // 处理初始能力等级
            JSONArray initialLevelAbilityElderly = assessmentResult.getJSONArray("initialLevelAbilityElderly");
            if (initialLevelAbilityElderly != null && initialLevelAbilityElderly.size() > 1) {
                data.put("initialLevelAbilityElderly", initialLevelAbilityElderly.get(1).toString());
            } else {
                data.put("initialLevelAbilityElderly", " ");
            }

            // 处理能力等级变化
            processBasisAbilityLevelChange(assessmentResult, data);

            // 处理最终等级
            JSONArray finalLevel = assessmentResult.getJSONArray("finalLevel");
            if (finalLevel != null && finalLevel.size() > 1) {
                data.put("finalLevel", finalLevel.get(1).toString());
            } else {
                data.put("finalLevel", " ");
            }
        } else {
            // 如果评估计划或结果为空，设置默认值
            setDefaultAssessmentResultValues(data);
        }
    }

    /**
     * 处理一级指标
     */
    private void processFirstLevelIndicator(JSONObject assessmentResult, Map<String, Object> data) {
        JSONObject firstLevelIndicator = assessmentResult.getJSONObject("firstLevelIndicator");
        if (firstLevelIndicator != null) {
            data.put("selfCareAbility", Optional.ofNullable(firstLevelIndicator.get("selfCareAbility")).orElse(" ").toString());
            data.put("basicMotorAbility", Optional.ofNullable(firstLevelIndicator.get("basicMotorAbility")).orElse(" ").toString());
            data.put("mentalState", Optional.ofNullable(firstLevelIndicator.get("mentalState")).orElse(" ").toString());
            data.put("socialParticipation", Optional.ofNullable(firstLevelIndicator.get("socialParticipation")).orElse(" ").toString());
        } else {
            data.put("selfCareAbility", " ");
            data.put("basicMotorAbility", " ");
            data.put("mentalState", " ");
            data.put("socialParticipation", " ");
        }
    }

    /**
     * 处理能力等级变化
     */
    private void processBasisAbilityLevelChange(JSONObject assessmentResult, Map<String, Object> data) {
        JSONArray basisAbilityLevelChange = assessmentResult.getJSONArray("basisAbilityLevelChange");
        if (basisAbilityLevelChange != null) {
            List<String> titles = new ArrayList<>();
            ObjectMapper mapper = new ObjectMapper();
            try {
                JsonNode nodes = mapper.readTree(basisAbilityLevelChange.toJSONString());
                for (JsonNode node : nodes) {
                    if (node.has("title")) {
                        titles.add(node.get("title").asText());
                    }
                }
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            data.put("basisAbilityLevelChange", String.join("，", titles));
        } else {
            data.put("basisAbilityLevelChange", " ");
        }
    }

    /**
     * 设置评估结果默认值
     */
    private void setDefaultAssessmentResultValues(Map<String, Object> data) {
        data.put("selfCareAbility", " ");
        data.put("basicMotorAbility", " ");
        data.put("mentalState", " ");
        data.put("socialParticipation", " ");
        data.put("preliminaryGradeScore", " ");
        data.put("initialLevelAbilityElderly", " ");
        data.put("basisAbilityLevelChange", " ");
        data.put("finalLevel", " ");
    }

    private void updateMarketingCustomerInfo(MarketingCustomerInfo marketingCustomerInfo, JSONObject assessmentObjBasicsInfo) {
        // 更新存在于营销客户表中的字段
        if (assessmentObjBasicsInfo.containsKey("elderlyName")) {
            marketingCustomerInfo.setElderName(assessmentObjBasicsInfo.getString("elderlyName"));
        }
        if (assessmentObjBasicsInfo.containsKey("sex")) {
            marketingCustomerInfo.setElderGender(assessmentObjBasicsInfo.getString("sex").equals("男") ? "0" : "1");
        }
        if (assessmentObjBasicsInfo.containsKey("age")) {
            marketingCustomerInfo.setElderAge(assessmentObjBasicsInfo.getInteger("age"));
        }
        if (assessmentObjBasicsInfo.containsKey("ID")) {
            marketingCustomerInfo.setIdCardNumber(assessmentObjBasicsInfo.getString("ID"));
        }
        if (assessmentObjBasicsInfo.containsKey("elderlyDateBirth")) {
            try {
                String dateStr = assessmentObjBasicsInfo.getString("elderlyDateBirth");
                if (dateStr != null && !dateStr.isEmpty()) {
                    marketingCustomerInfo.setElderBirthday(DateUtil.parseDate(dateStr));
                }
            } catch (Exception e) {
                // 日期解析失败时忽略
            }
        }
        if (assessmentObjBasicsInfo.containsKey("nationality")) {
            marketingCustomerInfo.setNation(assessmentObjBasicsInfo.getString("nationality"));
        }
        // if (assessmentObjBasicsInfo.containsKey("maritalStatus")) {
        //     marketingCustomerInfo.setMaritalStatus(assessmentObjBasicsInfo.getString("maritalStatus"));
        // }

        // if (assessmentObjBasicsInfo.containsKey("LivingSituation")) {
        //     JSONArray livingSituation = assessmentObjBasicsInfo.getJSONArray("LivingSituation");
        //     if (livingSituation != null && !livingSituation.isEmpty()) {
        //         // 根据居住情况数组设置居住状态（需要根据实际业务逻辑映射）
        //         String residenceStatus = getStringValue(livingSituation);
        //         if (residenceStatus.contains("独居")) {
        //             marketingCustomerInfo.setResidenceStatus("0");
        //         } else if (residenceStatus.contains("子女")) {
        //             marketingCustomerInfo.setResidenceStatus("1");
        //         } else if (residenceStatus.contains("夫妻") || residenceStatus.contains("配偶")) {
        //             marketingCustomerInfo.setResidenceStatus("2");
        //         }
        //     }
        // }
    }

    private void updateElderlyPeopleInfo(ElderlyPeopleInfo elderlyPeopleInfo, JSONObject assessmentObjBasicsInfo) {
        // 更新存在于老人信息表中的字段
        if (assessmentObjBasicsInfo.containsKey("elderlyName")) {
            elderlyPeopleInfo.setName(assessmentObjBasicsInfo.getString("elderlyName"));
        }
        if (assessmentObjBasicsInfo.containsKey("sex")) {
            elderlyPeopleInfo.setSex(assessmentObjBasicsInfo.getString("sex").equals("男") ? "0" : "1");
        }
        if (assessmentObjBasicsInfo.containsKey("age")) {
            elderlyPeopleInfo.setAge(assessmentObjBasicsInfo.getInteger("age"));
        }
        if (assessmentObjBasicsInfo.containsKey("elderlyDateBirth")) {
            try {
                String dateStr = assessmentObjBasicsInfo.getString("elderlyDateBirth");
                if (dateStr != null && !dateStr.isEmpty()) {
                    elderlyPeopleInfo.setDateBirth(DateUtil.parseDate(dateStr));
                }
            } catch (Exception e) {
                // 日期解析失败时忽略
            }
        }
        if (assessmentObjBasicsInfo.containsKey("ID")) {
            elderlyPeopleInfo.setIdCardNum(assessmentObjBasicsInfo.getString("ID"));
        }
        if (assessmentObjBasicsInfo.containsKey("nationality")) {
            elderlyPeopleInfo.setNation(assessmentObjBasicsInfo.getString("nationality"));
        }
        // if (assessmentObjBasicsInfo.containsKey("maritalStatus")) {
        //     elderlyPeopleInfo.setMarriageStatus(assessmentObjBasicsInfo.getString("maritalStatus"));
        // }
        // if (assessmentObjBasicsInfo.containsKey("LivingSituation")) {
        //     JSONArray livingSituation = assessmentObjBasicsInfo.getJSONArray("LivingSituation");
        //     if (livingSituation != null && !livingSituation.isEmpty()) {
        //         elderlyPeopleInfo.setLivingSituation(getStringValue(livingSituation));
        //     }
        // }
        // if (assessmentObjBasicsInfo.containsKey("EconomicSource")) {
        //     JSONArray economicSource = assessmentObjBasicsInfo.getJSONArray("EconomicSource");
        //     if (economicSource != null && !economicSource.isEmpty()) {
        //         elderlyPeopleInfo.setEconomicSources(getStringValue(economicSource));
        //     }
        // }
    }

}
